import React from 'react';
import Svg, { G, <PERSON> } from 'react-native-svg';

interface RMBCardProps {
  width?: number;
  height?: number;
  color?: string;
}

export const RMBCard: React.FC<RMBCardProps> = ({ 
  width = 120, 
  height = 75, 
  color = '#2c3e50' 
}) => {
  return (
    <Svg 
      width={width} 
      height={height} 
      viewBox="0 0 1547.3 623.7"
      style={{ backgroundColor: color, borderRadius: 8 }}
    >
      <G>
        <Path
          d="M378,210.2c10,0,19.3,3.1,28.1,8.7l31.8,19.9l29.3-8.7l43-51.8l-91.7-91.7c-36.1-36.1-87.9-56.1-144.7-56.1
          h-1.3C246.1,11.3,208,0,168.5,0c-16.6,0.1-33.1,2-49.3,5.6C126,25.2,140,48,150.9,64C61.5,115.6,0,221.3,0,343
          c0,24.3,2.5,36.8,2.5,36.8s29.4-29.9,68.1-29.9c0,59.9-38,117.9-38,117.9c96.7,0,269.4,45.5,331.1,87.3
          c0-28.7-61.7-100.4-121-139.1c0,0,174,75.5,200.2,207.7c0,0,13.1-27.4,13.1-72.3c0-199.6-257.6-194-259.4-359.8
          c-0.6-44.3,17.5-71.7,17.5-71.7c-4,17-6.1,34.4-6.3,51.8c0,127.8,134.1,133.5,218.3,213.9l18.1-97.9l-34.9,11.2l-25.6-7.5
          c-28.1-8.1-44.3-24.3-44.3-44.9S355.5,210.2,378,210.2z M271.9,108.1c0,0,21.2-2.5,36.2-2.5c31.2,0,61.1,10.6,79.2,26.8l-18.7,22.5
          C352.4,136.2,300,110.6,271.9,108.1z"
          fill="white"
        />
        <Path
          d="M822.4,374.7l-27.2-38.8c38.1-15.1,61-45.7,61-82.8c0-49.2-40.3-83-99-83H623.6l4.4,6.8
          c9.6,14.7,14.7,28.4,14.7,41v177.6c0,12.6-5.1,26.3-14.7,41l-4.4,6.8h76.5v-222h53.6c26.3,0,44,14.7,44,36.2
          c0,25.3-21.2,41.3-80.9,56l65.6,101.1c16.4,25.3,41,38.9,70.3,38.9c27,0,37.9-11.3,37.9-11.3S863.4,433.4,822.4,374.7z"
          fill="white"
        />
        <Path
          d="M1256,443.3l-4.4-5.5c-9.6-11.6-13.7-23.2-14.7-33.5l-20.8-186.5c-1.4-12.6,3.1-26,11.3-41l3.8-6.8h-80.6
          l-70.4,155.2l-70.3-155.2h-80.6l3.8,6.8c8.2,15,12.6,28.4,11.3,41l-20.8,186.5c-1,10.2-5.1,21.9-14.7,33.5l-4.4,5.5h74.1L995,266.3
          l85.2,187.3l85.2-187.3l16.6,177.1L1256,443.3z"
          fill="white"
        />
        <Path
          d="M1484.1,298.3c19.9-11.4,30.9-30.1,30.9-54.4c0-44-36.2-73.8-89.8-73.8h-133.9l4.4,6.8
          c9.6,14.4,14.7,28.4,14.7,41v177.6c0,12.6-5.1,26.6-14.7,41l-4.4,6.8h145.1c55,0,94.3-33.5,94.3-80.9
          C1530.7,331.5,1514.1,309.3,1484.1,298.3z M1419.1,219c23.6,0,40,13,40,31.8s-16.4,31.8-40,31.8h-51.2V219H1419.1z M1431.1,394.5
          h-63.2v-66.9h63.2c24.6,0,40.6,13,40.6,33.5S1455.5,394.5,1431.1,394.5L1431.1,394.5z"
          fill="white"
        />
      </G>
    </Svg>
  );
}; 