import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';
import { HARDCODED_USER } from '../utils/data';

interface MoreScreenProps {
  navigation: any;
}

export const MoreScreen: React.FC<MoreScreenProps> = ({ navigation }) => {
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => navigation.replace('Login')
        },
      ]
    );
  };

  const renderMenuItem = (
    icon: string,
    title: string,
    subtitle: string,
    onPress: () => void,
    showChevron: boolean = true
  ) => (
    <TouchableOpacity style={styles.menuItem} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.menuIcon}>
        <Ionicons name={icon as any} size={24} color={Colors.primary} />
      </View>
      <View style={styles.menuContent}>
        <Text style={styles.menuTitle}>{title}</Text>
        <Text style={styles.menuSubtitle}>{subtitle}</Text>
      </View>
      {showChevron && (
        <Ionicons name="chevron-forward" size={20} color={Colors.textMuted} />
      )}
    </TouchableOpacity>
  );

  const renderMenuSection = (title: string, items: any[]) => (
    <View style={styles.menuSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.menuGroup}>
        {items.map((item, index) => (
          <View key={index}>
            {renderMenuItem(
              item.icon,
              item.title,
              item.subtitle,
              item.onPress,
              item.showChevron
            )}
            {index < items.length - 1 && <View style={styles.menuDivider} />}
          </View>
        ))}
      </View>
    </View>
  );

  const bankingServices = [
    {
      icon: 'card-outline',
      title: 'Manage Cards',
      subtitle: 'Block, activate, or order new cards',
      onPress: () => {},
    },
    {
      icon: 'receipt-outline',
      title: 'Buy Airtime & Data',
      subtitle: 'Top up your mobile',
      onPress: () => {},
    },
    {
      icon: 'qr-code-outline',
      title: 'QR Payments',
      subtitle: 'Scan to pay or receive payments',
      onPress: () => {},
    },
    {
      icon: 'flash-outline',
      title: 'Pay Bills',
      subtitle: 'Electricity, water, municipal bills',
      onPress: () => {},
    },
  ];

  const investmentServices = [
    {
      icon: 'trending-up-outline',
      title: 'Investments',
      subtitle: 'View your investment portfolio',
      onPress: () => {},
    },
    {
      icon: 'library-outline',
      title: 'Fixed Deposits',
      subtitle: 'Manage your fixed deposits',
      onPress: () => {},
    },
    {
      icon: 'pie-chart-outline',
      title: 'Unit Trusts',
      subtitle: 'Track your unit trust investments',
      onPress: () => {},
    },
  ];

  const supportServices = [
    {
      icon: 'headset-outline',
      title: 'Contact Support',
      subtitle: '24/7 customer support',
      onPress: () => {},
    },
    {
      icon: 'location-outline',
      title: 'Find Branch/ATM',
      subtitle: 'Locate nearest branch or ATM',
      onPress: () => {},
    },
    {
      icon: 'document-text-outline',
      title: 'Statements',
      subtitle: 'Download account statements',
      onPress: () => {},
    },
    {
      icon: 'settings-outline',
      title: 'Settings',
      subtitle: 'App preferences and security',
      onPress: () => {},
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* User Profile */}
        <LinearGradient
          colors={[Colors.primary, Colors.primaryDark]}
          style={styles.profileCard}
        >
          <View style={styles.profileInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {HARDCODED_USER.name.split(' ').map(n => n[0]).join('')}
              </Text>
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{HARDCODED_USER.name}</Text>
              <Text style={styles.userAccount}>
                Account: ****{HARDCODED_USER.accountNumber.slice(-4)}
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.editProfile}>
            <Ionicons name="create-outline" size={20} color={Colors.text} />
          </TouchableOpacity>
        </LinearGradient>

        {/* Banking Services */}
        {renderMenuSection('Banking Services', bankingServices)}

        {/* Investment Services */}
        {renderMenuSection('Investments', investmentServices)}

        {/* Support & Settings */}
        {renderMenuSection('Support & Settings', supportServices)}

        {/* Logout */}
        <View style={styles.menuSection}>
          <View style={styles.menuGroup}>
            {renderMenuItem(
              'log-out-outline',
              'Logout',
              'Sign out of your account',
              handleLogout,
              false
            )}
          </View>
        </View>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>RMB Private Banking</Text>
          <Text style={styles.appVersion}>Version 1.0.0</Text>
          <Text style={styles.copyright}>© 2024 RMB Private Bank</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  profileCard: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 24,
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.text,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  userAccount: {
    fontSize: 14,
    color: Colors.text,
    opacity: 0.8,
    marginTop: 4,
  },
  editProfile: {
    padding: 8,
  },
  menuSection: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  menuGroup: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    overflow: 'hidden',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  menuSubtitle: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 2,
  },
  menuDivider: {
    height: 1,
    backgroundColor: Colors.divider,
    marginLeft: 76,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  appInfoText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 12,
    color: Colors.textMuted,
    marginBottom: 8,
  },
  copyright: {
    fontSize: 10,
    color: Colors.textMuted,
  },
}); 