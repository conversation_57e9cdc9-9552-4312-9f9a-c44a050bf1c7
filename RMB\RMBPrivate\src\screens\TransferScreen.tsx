import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';
import { HARDCODED_ACCOUNTS, HARDCODED_BENEFICIARIES } from '../utils/data';

interface TransferScreenProps {
  navigation: any;
}

export const TransferScreen: React.FC<TransferScreenProps> = ({ navigation }) => {
  const [fromAccount, setFromAccount] = useState(HARDCODED_ACCOUNTS[0]);
  const [toAccount, setToAccount] = useState('');
  const [amount, setAmount] = useState('');
  const [reference, setReference] = useState('');

  const formatCurrency = (amount: number) => {
    return `R ${amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`;
  };

  const handleTransfer = () => {
    if (!toAccount || !amount || parseFloat(amount) <= 0) {
      Alert.alert('Invalid Transfer', 'Please fill in all required fields');
      return;
    }

    if (parseFloat(amount) > fromAccount.balance) {
      Alert.alert('Insufficient Funds', 'You do not have enough balance for this transfer');
      return;
    }

    Alert.alert(
      'Confirm Transfer',
      `Transfer ${formatCurrency(parseFloat(amount))} to ${toAccount}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Confirm', 
          onPress: () => {
            Alert.alert('Success', 'Transfer completed successfully');
            setAmount('');
            setToAccount('');
            setReference('');
          }
        },
      ]
    );
  };

  const renderBeneficiary = (beneficiary: any) => (
    <TouchableOpacity
      key={beneficiary.id}
      style={styles.beneficiaryItem}
      onPress={() => setToAccount(beneficiary.accountNumber)}
      activeOpacity={0.7}
    >
      <View style={styles.beneficiaryIcon}>
        <Ionicons name="person" size={20} color={Colors.primary} />
      </View>
      <View style={styles.beneficiaryInfo}>
        <Text style={styles.beneficiaryName}>{beneficiary.name}</Text>
        <Text style={styles.beneficiaryBank}>{beneficiary.bankName}</Text>
        <Text style={styles.beneficiaryAccount}>****{beneficiary.accountNumber.slice(-4)}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Transfer Money</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* From Account */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>From Account</Text>
          <View style={styles.accountSelector}>
            <LinearGradient
              colors={[Colors.card, Colors.surface]}
              style={styles.selectedAccount}
            >
              <View style={styles.accountInfo}>
                <Text style={styles.accountName}>{fromAccount.name}</Text>
                <Text style={styles.accountNumber}>****{fromAccount.accountNumber.slice(-4)}</Text>
              </View>
              <Text style={styles.accountBalance}>{formatCurrency(fromAccount.balance)}</Text>
            </LinearGradient>
          </View>
        </View>

        {/* To Account */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>To Account</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter account number"
            placeholderTextColor={Colors.placeholder}
            value={toAccount}
            onChangeText={setToAccount}
            keyboardType="numeric"
          />
        </View>

        {/* Amount */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Amount</Text>
          <View style={styles.amountContainer}>
            <Text style={styles.currencySymbol}>R</Text>
            <TextInput
              style={styles.amountInput}
              placeholder="0.00"
              placeholderTextColor={Colors.placeholder}
              value={amount}
              onChangeText={setAmount}
              keyboardType="decimal-pad"
            />
          </View>
        </View>

        {/* Reference */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reference (Optional)</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter payment reference"
            placeholderTextColor={Colors.placeholder}
            value={reference}
            onChangeText={setReference}
          />
        </View>

        {/* Quick Amounts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Amounts</Text>
          <View style={styles.quickAmounts}>
            {[100, 200, 500, 1000].map((quickAmount) => (
              <TouchableOpacity
                key={quickAmount}
                style={styles.quickAmountButton}
                onPress={() => setAmount(quickAmount.toString())}
                activeOpacity={0.7}
              >
                <Text style={styles.quickAmountText}>R{quickAmount}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Beneficiaries */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Beneficiaries</Text>
          <View style={styles.beneficiariesList}>
            {HARDCODED_BENEFICIARIES.map(renderBeneficiary)}
          </View>
        </View>

        {/* Transfer Button */}
        <TouchableOpacity
          style={[styles.transferButton, { opacity: amount && toAccount ? 1 : 0.5 }]}
          onPress={handleTransfer}
          disabled={!amount || !toAccount}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[Colors.primary, Colors.primaryDark]}
            style={styles.transferButtonGradient}
          >
            <Text style={styles.transferButtonText}>Transfer Now</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  accountSelector: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  selectedAccount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  accountNumber: {
    fontSize: 14,
    color: Colors.textMuted,
    marginTop: 4,
  },
  accountBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.success,
  },
  input: {
    backgroundColor: Colors.inputBackground,
    borderColor: Colors.inputBorder,
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: Colors.inputText,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.inputBackground,
    borderColor: Colors.inputBorder,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    color: Colors.inputText,
    paddingVertical: 16,
  },
  quickAmounts: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    width: '23%',
    backgroundColor: Colors.card,
    borderColor: Colors.border,
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  quickAmountText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
  },
  beneficiariesList: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    overflow: 'hidden',
  },
  beneficiaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.divider,
  },
  beneficiaryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  beneficiaryInfo: {
    flex: 1,
  },
  beneficiaryName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  beneficiaryBank: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 2,
  },
  beneficiaryAccount: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  transferButton: {
    marginVertical: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  transferButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  transferButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
}); 