import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';

interface FNBHomeScreenProps {
  navigation: any;
}

export const FNBHomeScreen: React.FC<FNBHomeScreenProps> = ({ navigation }) => {
  const services = [
    // Row 1
    { icon: 'hand-left-outline', title: 'Pay', color: '#4db6ac' },
    { icon: 'storefront-outline', title: 'Product Shop', color: '#ff7043' },
    { icon: 'basket-outline', title: 'Buy', color: '#4db6ac' },
    { icon: 'chatbubble-outline', title: 'Message', color: '#ab47bc' },
    
    // Row 2
    { icon: 'swap-horizontal-outline', title: 'Transfer', color: '#4db6ac' },
    { icon: 'card-outline', title: 'Cards', color: '#4db6ac' },
    { icon: 'lock-closed-outline', title: 'Secure chat', color: '#4db6ac' },
    { icon: 'chevron-forward', title: 'nav-igate life', color: '#4db6ac' },
    
    // Row 3
    { icon: 'ellipse', title: 'eBucks', color: '#4db6ac' },
    { icon: 'document-text-outline', title: 'Statements', color: '#4db6ac' },
    { icon: 'star-outline', title: 'My offers', color: '#ffca28' },
    { icon: 'information-circle-outline', title: 'Information', color: '#4db6ac' },
    
    // Row 4
    { icon: 'card', title: 'Cardless cash withdrawal', color: '#4db6ac' },
    { icon: 'airplane-outline', title: 'Airport Lounges', color: '#66bb6a' },
    { icon: 'business-outline', title: 'Accounts', color: '#4db6ac' },
    { icon: 'add-circle-outline', title: '', color: '#ff4757' }, // Add button
  ];

  const renderService = (service: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.serviceItem}
      activeOpacity={0.7}
      onPress={() => {
        if (service.title === 'Accounts') {
          navigation.navigate('BankingHome');
        }
      }}
    >
      <View style={styles.serviceIconContainer}>
        <Ionicons name={service.icon as any} size={24} color={service.color} />
      </View>
      <Text style={styles.serviceText}>{service.title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.headerTitle}>RMB</Text>
          <TouchableOpacity style={styles.qrButton}>
            <Ionicons name="qr-code-outline" size={24} color={Colors.text} />
          </TouchableOpacity>
        </View>
        
        {/* Teal Banner */}
        <View style={styles.tealBanner} />
        
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search" size={20} color={Colors.primary} />
            <TextInput
              style={styles.searchInput}
              placeholder="How can we help you?"
              placeholderTextColor="#999"
            />
          </View>
        </View>
      </View>

      {/* Services Grid */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.servicesGrid}>
          {Array.from({ length: 4 }, (_, rowIndex) => (
            <View key={rowIndex} style={styles.serviceRow}>
              {services.slice(rowIndex * 4, (rowIndex + 1) * 4).map((service, colIndex) => 
                renderService(service, rowIndex * 4 + colIndex)
              )}
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="home" size={24} color={Colors.primary} />
          <Text style={[styles.navText, { color: Colors.primary }]}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('BankingHome')}
        >
          <Ionicons name="business-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Bank</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="chatbubble-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Message</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="person-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>My profile</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="menu-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Menu</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.background,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.text,
  },
  qrButton: {
    padding: 4,
  },
  tealBanner: {
    height: 40,
    backgroundColor: Colors.primary,
    marginHorizontal: 0,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  servicesGrid: {
    paddingTop: 20,
  },
  serviceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  serviceItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 16,
    paddingVertical: 20,
    paddingHorizontal: 8,
    marginHorizontal: 4,
  },
  serviceIconContainer: {
    marginBottom: 12,
  },
  serviceText: {
    fontSize: 12,
    color: Colors.text,
    textAlign: 'center',
    lineHeight: 16,
    fontWeight: '500',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingBottom: 20,
    paddingTop: 10,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
}); 