import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Modal,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';
import { HARDCODED_USER } from '../utils/data';

interface LoginScreenProps {
  navigation: any;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showBiometric, setShowBiometric] = useState(false);

  const handleLogin = async () => {
    if (password.length === 0) {
      Alert.alert('Invalid Password', 'Please enter your password');
      return;
    }

    setIsLoading(true);

    // Simulate authentication delay
    setTimeout(() => {
      if (password === 'password' || password === '78623') {
        setIsLoading(false);
        setShowBiometric(true);
      } else {
        Alert.alert('Authentication Failed', 'Invalid password. Please try again.');
        setPassword('');
        setIsLoading(false);
      }
    }, 1000);
  };

  const handleBiometricAuth = () => {
    // Simulate biometric authentication
    setTimeout(() => {
      setShowBiometric(false);
      navigation.navigate('BankingHome');
    }, 1500);
  };

  const handleForgotPassword = () => {
    Alert.alert('Forgot Password', 'Please contact your branch for password reset assistance.');
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[Colors.background, Colors.surface]}
        style={styles.gradient}
      >
        <KeyboardAvoidingView
          style={styles.content}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity 
              style={styles.backButton} 
              onPress={() => navigation.goBack()}
              activeOpacity={0.7}
            >
              <Ionicons name="arrow-back" size={24} color={Colors.text} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>RMB</Text>
          </View>

          {/* Logo */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Image 
                source={require('../../images/RMB_icon.jpeg')} 
                style={styles.logoImage}
                resizeMode="cover"
              />
            </View>
          </View>

          {/* Welcome Message */}
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeText}>Hi N Dasarath, please enter your RMB App password to</Text>
            <Text style={styles.welcomeText}>login</Text>
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Password</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={true}
                placeholder=""
                placeholderTextColor={Colors.placeholder}
                autoCapitalize="none"
              />
            </View>
          </View>

          {/* Login Button */}
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            <Text style={styles.loginButtonText}>
              {isLoading ? 'Authenticating...' : 'Login'}
            </Text>
          </TouchableOpacity>

          {/* Forgot Password */}
          <TouchableOpacity
            style={styles.forgotButton}
            onPress={handleForgotPassword}
            activeOpacity={0.7}
          >
            <Text style={styles.forgotButtonText}>Forgot Password</Text>
          </TouchableOpacity>


        </KeyboardAvoidingView>
      </LinearGradient>

      {/* Biometric Authentication Modal */}
      <Modal
        visible={showBiometric}
        transparent={true}
        animationType="slide"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.biometricContainer}>
            <LinearGradient
              colors={['#6a4c93', '#9b59b6']}
              style={styles.biometricGradient}
            >
              <View style={styles.biometricHeader}>
                <View style={styles.biometricLogoContainer}>
                  <Image 
                    source={require('../../images/RMB_icon.jpeg')} 
                    style={styles.biometricLogoImage}
                    resizeMode="cover"
                  />
                </View>
                <Text style={styles.biometricTitle}>RMB</Text>
                <Text style={styles.biometricSubtitle}>Login</Text>
                <Text style={styles.biometricMessage}>Please authenticate to login</Text>
                <Text style={styles.biometricInstruction}>Touch the fingerprint sensor</Text>
              </View>

              <View style={styles.fingerprintContainer}>
                <TouchableOpacity
                  style={styles.fingerprintButton}
                  onPress={handleBiometricAuth}
                  activeOpacity={0.8}
                >
                  <View style={styles.fingerprintCircle}>
                    <Ionicons name="finger-print" size={40} color={Colors.text} />
                  </View>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setShowBiometric(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            </LinearGradient>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logoImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  welcomeContainer: {
    marginBottom: 40,
  },
  welcomeText: {
    fontSize: 16,
    color: '#5a9fd4',
    textAlign: 'left',
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.textMuted,
    marginBottom: 8,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.text,
    borderRadius: 25,
    paddingHorizontal: 16,
    height: 50,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.background,
    paddingVertical: 12,
  },
  showButton: {
    backgroundColor: '#6c757d',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 15,
  },
  showButtonText: {
    color: Colors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    backgroundColor: Colors.primary,
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  loginButtonText: {
    color: Colors.text,
    fontSize: 18,
    fontWeight: '600',
  },
  forgotButton: {
    backgroundColor: Colors.text,
    borderRadius: 25,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  forgotButtonText: {
    color: Colors.textMuted,
    fontSize: 16,
    fontWeight: '500',
  },
  hintContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  hintText: {
    fontSize: 12,
    color: Colors.textMuted,
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  biometricContainer: {
    height: '60%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  biometricGradient: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  biometricHeader: {
    alignItems: 'center',
    marginTop: 20,
  },
  biometricLogoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    overflow: 'hidden',
  },
  biometricLogoImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  biometricTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  biometricSubtitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 20,
  },
  biometricMessage: {
    fontSize: 16,
    color: Colors.text,
    marginBottom: 8,
  },
  biometricInstruction: {
    fontSize: 16,
    color: Colors.text,
  },
  fingerprintContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  fingerprintButton: {
    padding: 20,
  },
  fingerprintCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: Colors.text,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cancelButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
  },
  cancelButtonText: {
    color: Colors.text,
    fontSize: 16,
  },
}); 