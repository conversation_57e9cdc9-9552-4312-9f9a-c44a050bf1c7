import { User, Transaction, Account, Beneficiary } from '../types';

export const HARDCODED_USER: User = {
  id: '1',
  name: '<PERSON>',
  accountNumber: '****************',
  balance: 25750.50,
  pincode: '07045'
};

export const HARDCODED_ACCOUNTS: Account[] = [
  {
    id: '1',
    name: 'Current Account',
    accountNumber: '****************',
    balance: 25750.50,
    type: 'current'
  },
  {
    id: '2',
    name: 'Savings Account',
    accountNumber: '****************',
    balance: 45200.75,
    type: 'savings'
  },
  {
    id: '3',
    name: 'Credit Card',
    accountNumber: '****************',
    balance: -2150.00,
    type: 'credit'
  }
];

export const HARDCODED_TRANSACTIONS: Transaction[] = [
  {
    id: '1',
    type: 'credit',
    amount: 5000.00,
    description: 'Salary Payment',
    date: '2024-01-15',
    balance: 25750.50
  },
  {
    id: '2',
    type: 'debit',
    amount: 150.00,
    description: 'ATM Withdrawal',
    date: '2024-01-14',
    balance: 20750.50
  },
  {
    id: '3',
    type: 'debit',
    amount: 85.50,
    description: 'Online Purchase - Amazon',
    date: '2024-01-13',
    balance: 20900.50
  },
  {
    id: '4',
    type: 'credit',
    amount: 200.00,
    description: 'Transfer from Mom',
    date: '2024-01-12',
    balance: 20986.00
  },
  {
    id: '5',
    type: 'debit',
    amount: 1200.00,
    description: 'Rent Payment',
    date: '2024-01-10',
    balance: 20786.00
  },
  {
    id: '6',
    type: 'debit',
    amount: 45.00,
    description: 'Uber Ride',
    date: '2024-01-09',
    balance: 21986.00
  }
];

export const HARDCODED_BENEFICIARIES: Beneficiary[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    accountNumber: '****************',
    bankName: 'Standard Bank'
  },
  {
    id: '2',
    name: 'Michael Smith',
    accountNumber: '****************',
    bankName: 'ABSA Bank'
  },
  {
    id: '3',
    name: 'Emma Wilson',
    accountNumber: '****************',
    bankName: 'Nedbank'
  }
]; 