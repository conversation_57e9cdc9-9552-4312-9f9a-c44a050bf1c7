export interface User {
  id: string;
  name: string;
  accountNumber: string;
  balance: number;
  pincode: string;
}

export interface Transaction {
  id: string;
  type: 'debit' | 'credit';
  amount: number;
  description: string;
  date: string;
  balance: number;
}

export interface Account {
  id: string;
  name: string;
  accountNumber: string;
  balance: number;
  type: 'current' | 'savings' | 'credit';
}

export interface Beneficiary {
  id: string;
  name: string;
  accountNumber: string;
  bankName: string;
}

export type RootStackParamList = {
  Login: undefined;
  PinLogin: undefined;
  Home: undefined;
  Account: { accountId: string };
  Transfer: undefined;
  Transactions: undefined;
  Profile: undefined;
};

export type TabParamList = {
  Dashboard: undefined;
  Accounts: undefined;
  Transfer: undefined;
  Transactions: undefined;
  More: undefined;
}; 