import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';

const { width } = Dimensions.get('window');

interface BankingHomeScreenProps {
  navigation: any;
}

export const BankingHomeScreen: React.FC<BankingHomeScreenProps> = ({ navigation }) => {
  const formatCurrency = (amount: number) => {
    return `R${amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`;
  };

  const accounts = [
    {
      id: '1',
      name: 'RMB Ai NexGen',
      availableBalance: 0,
      actualBalance: 0,
      type: 'Day to day'
    },
    {
      id: '2',
      name: 'RMB Private Clients',
      availableBalance: 0,
      actualBalance: 0,
      type: 'Credit Card'
    }
  ];

  const quickActions = [
    { icon: 'card-outline', title: 'Pay', onPress: () => {} },
    { icon: 'paper-plane-outline', title: 'Transfer', onPress: () => {} },
    { icon: 'card', title: 'My Cards', onPress: () => {} },
    { icon: 'arrow-down-outline', title: 'Withdraw', onPress: () => {} },
  ];

  const renderQuickAction = (action: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.quickActionItem}
      onPress={action.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.quickActionIcon}>
        <Ionicons name={action.icon as any} size={20} color={Colors.textMuted} />
      </View>
      <Text style={styles.quickActionText}>{action.title}</Text>
    </TouchableOpacity>
  );

  const renderAccount = (account: any) => (
    <TouchableOpacity
      key={account.id}
      style={styles.accountCard}
      activeOpacity={0.8}
      onPress={() => navigation.navigate('TransactionHistory', { accountName: account.name })}
    >
      <View style={styles.accountHeader}>
        <Text style={styles.accountType}>{account.type}</Text>
        <Text style={styles.accountBalance}>
          {formatCurrency(account.availableBalance)}
        </Text>
      </View>
      
      <View style={styles.accountDetails}>
        <Text style={styles.accountName}>{account.name}</Text>
        <View style={styles.balanceInfo}>
          <Text style={styles.balanceLabel}>Avail {formatCurrency(account.availableBalance)}</Text>
          <Text style={styles.balanceLabel}>
            Bal {formatCurrency(Math.abs(account.actualBalance))}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Accounts</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerIcon}>
              <Ionicons name="search-outline" size={24} color={Colors.textMuted} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerIcon}>
              <Ionicons name="refresh-outline" size={24} color={Colors.textMuted} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Welcome Banner */}
        <View style={styles.welcomeBanner}>
          <LinearGradient
            colors={[Colors.primary, Colors.primaryLight]}
            style={styles.bannerGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <View style={styles.bannerContent}>
              <Text style={styles.bannerTitle}>Welcome to</Text>
              <Text style={styles.bannerSubtitle}>Banking</Text>
            </View>
            <View style={styles.bannerImage}>
              {/* Building illustration placeholder */}
              <View style={styles.buildingPlaceholder}>
                <Ionicons name="business" size={60} color="rgba(255,255,255,0.8)" />
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <View style={styles.quickActionsRow}>
            {quickActions.map((action, index) => renderQuickAction(action, index))}
          </View>
        </View>

        {/* Add Account */}
        <TouchableOpacity style={styles.addAccountButton} activeOpacity={0.7}>
          <View style={styles.addAccountIcon}>
            <Ionicons name="add" size={20} color="#ff4757" />
          </View>
          <Text style={styles.addAccountText}>Add Account</Text>
        </TouchableOpacity>

        {/* Accounts List */}
        <View style={styles.accountsList}>
          {accounts.map(renderAccount)}
        </View>

        {/* Track my Spend */}
        <TouchableOpacity style={styles.trackSpendCard} activeOpacity={0.8}>
          <Text style={styles.trackSpendText}>Track my Spend</Text>
          <View style={styles.trackSpendChart}>
            {/* Simple chart placeholder */}
            <View style={styles.chartPlaceholder}>
              <Ionicons name="analytics" size={30} color="#4285f4" />
            </View>
          </View>
        </TouchableOpacity>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('FNBHome')}
        >
          <Ionicons name="home-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="business-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Bank</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="chatbubble-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Message</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="person-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>My profile</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="menu-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Menu</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.text,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerIcon: {
    marginLeft: 16,
    padding: 4,
  },
  welcomeBanner: {
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 16,
    overflow: 'hidden',
    height: 140,
  },
  bannerGradient: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  bannerContent: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  bannerSubtitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text,
  },
  bannerImage: {
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buildingPlaceholder: {
    opacity: 0.8,
  },
  quickActionsContainer: {
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  quickActionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickActionItem: {
    alignItems: 'center',
    flex: 1,
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 12,
    color: Colors.textMuted,
    textAlign: 'center',
  },
  addAccountButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  addAccountIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#ff4757',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  addAccountText: {
    fontSize: 16,
    color: '#ff4757',
    fontWeight: '500',
  },
  accountsList: {
    paddingHorizontal: 20,
  },
  accountCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  accountHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  accountType: {
    fontSize: 14,
    color: Colors.textMuted,
    fontWeight: '500',
  },
  accountBalance: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  accountDetails: {
    marginBottom: 8,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  balanceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  balanceLabel: {
    fontSize: 12,
    color: Colors.textMuted,
  },
  trackSpendCard: {
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 16,
    marginBottom: 100,
    marginTop: 12,
  },
  trackSpendText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 12,
  },
  trackSpendChart: {
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartPlaceholder: {
    opacity: 0.6,
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingBottom: 20,
    paddingTop: 10,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
}); 