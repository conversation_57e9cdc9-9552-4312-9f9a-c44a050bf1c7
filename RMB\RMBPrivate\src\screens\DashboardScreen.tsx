import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';

const { width } = Dimensions.get('window');

interface DashboardScreenProps {
  navigation: any;
}

export const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {

  const renderServiceItem = (icon: string, title: string, onPress: () => void, isHighlighted: boolean = false) => (
    <TouchableOpacity 
      style={[styles.serviceItem, isHighlighted && styles.highlightedService]} 
      onPress={onPress} 
      activeOpacity={0.7}
    >
      <View style={[styles.serviceIcon, isHighlighted && styles.highlightedIcon]}>
        <Ionicons 
          name={icon as any} 
          size={24} 
          color={isHighlighted ? Colors.background : Colors.text} 
        />
      </View>
      <Text style={[styles.serviceText, isHighlighted && styles.highlightedText]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const firstRowServices = [
    { icon: 'log-in-outline', title: 'Login', onPress: () => navigation.navigate('Login'), highlighted: true },
    { icon: 'chatbubble-outline', title: 'Message', onPress: () => {}, highlighted: false },
    { icon: 'qr-code-outline', title: 'Show my QR code', onPress: () => {}, highlighted: false },
    { icon: 'call-outline', title: 'Contact us', onPress: () => {}, highlighted: false },
  ];

  const secondRowServices = [
    { icon: 'basket-outline', title: 'Product Shop', onPress: () => {}, highlighted: true },
    { icon: 'hand-left-outline', title: 'Pay', onPress: () => {}, highlighted: false },
    { icon: 'person-outline', title: 'My profile', onPress: () => {}, highlighted: false },
    { icon: 'paper-plane-outline', title: 'Transfer', onPress: () => {}, highlighted: false },
  ];

  const thirdRowServices = [
    { icon: 'card-outline', title: 'Cards', onPress: () => {}, highlighted: false },
    { icon: 'navigate-outline', title: 'nav-igate life', onPress: () => {}, highlighted: false },
    { icon: 'ellipse-outline', title: 'eBucks', onPress: () => {}, highlighted: false },
    { icon: 'settings-outline', title: 'Settings', onPress: () => {}, highlighted: false },
  ];

  const fourthRowServices = [
    { icon: 'information-circle-outline', title: 'Information', onPress: () => {}, highlighted: false },
    { icon: 'call-outline', title: 'Contact us', onPress: () => {}, highlighted: false },
    { icon: 'location-outline', title: 'ATM + Branch Locator', onPress: () => {}, highlighted: false },
    { icon: 'help-circle-outline', title: 'FAQs', onPress: () => {}, highlighted: false },
  ];

  const fifthRowServices = [
    { icon: 'phone-portrait-outline', title: 'eWallet', onPress: () => {}, highlighted: false },
    { icon: 'library-outline', title: 'RMB Keys', onPress: () => {}, highlighted: false },
    { icon: 'shield-outline', title: 'Security centre', onPress: () => {}, highlighted: false },
    { icon: 'home-outline', title: 'Lifestyle', onPress: () => {}, highlighted: false },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView} 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.appTitle}>RMB Private Bank</Text>
          </View>
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.headerIcon}>
              <Ionicons name="refresh-outline" size={24} color={Colors.text} />
            </TouchableOpacity>
            <View style={styles.flagIcon}>
              <Text style={styles.flagText}>🇿🇦</Text>
            </View>
          </View>
        </View>

        {/* Hero Banner */}
        <View style={styles.heroBanner}>
          <LinearGradient
            colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.3)']}
            style={styles.bannerOverlay}
          >
            <Text style={styles.bannerTitle}>ADVICE-LED SOLUTIONS</Text>
            <Text style={styles.bannerTitle}>FOR EVERY STAGE OF</Text>
            <Text style={styles.bannerTitle}>YOUR LIFE</Text>
            <Text style={styles.bannerSubtitle}>Annual pricing updates,</Text>
            <Text style={styles.bannerSubtitle}>effective from 1 July 2025</Text>
          </LinearGradient>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons name="search-outline" size={20} color={Colors.textMuted} />
            <TextInput
              style={styles.searchInput}
              placeholder="How can we help you?"
              placeholderTextColor={Colors.textMuted}
            />
          </View>
        </View>

        {/* Services Grid */}
        <View style={styles.servicesContainer}>
          {/* First Row */}
          <View style={styles.servicesRow}>
            {firstRowServices.map((service, index) => 
              <View key={`first-${index}`}>
                {renderServiceItem(service.icon, service.title, service.onPress, service.highlighted)}
              </View>
            )}
          </View>

          {/* Second Row */}
          <View style={styles.servicesRow}>
            {secondRowServices.map((service, index) => 
              <View key={`second-${index}`}>
                {renderServiceItem(service.icon, service.title, service.onPress, service.highlighted)}
              </View>
            )}
          </View>

          {/* Third Row */}
          <View style={styles.servicesRow}>
            {thirdRowServices.map((service, index) => 
              <View key={`third-${index}`}>
                {renderServiceItem(service.icon, service.title, service.onPress, service.highlighted)}
              </View>
            )}
          </View>

          {/* Fourth Row */}
          <View style={styles.servicesRow}>
            {fourthRowServices.map((service, index) => 
              <View key={`fourth-${index}`}>
                {renderServiceItem(service.icon, service.title, service.onPress, service.highlighted)}
              </View>
            )}
          </View>

          {/* Fifth Row */}
          <View style={styles.servicesRow}>
            {fifthRowServices.map((service, index) => 
              <View key={`fifth-${index}`}>
                {renderServiceItem(service.icon, service.title, service.onPress, service.highlighted)}
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="home" size={24} color={Colors.text} />
          <Text style={styles.navText}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="business-outline" size={24} color={Colors.textMuted} />
          <Text style={[styles.navText, { color: Colors.textMuted }]}>Bank</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="chatbubble-outline" size={24} color={Colors.textMuted} />
          <Text style={[styles.navText, { color: Colors.textMuted }]}>Message</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="card-outline" size={24} color={Colors.textMuted} />
          <Text style={[styles.navText, { color: Colors.textMuted }]}>Pay</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="menu-outline" size={24} color={Colors.textMuted} />
          <Text style={[styles.navText, { color: Colors.textMuted }]}>Menu</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  appTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: Colors.text,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 12,
  },
  flagIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flagText: {
    fontSize: 16,
  },
  heroBanner: {
    height: 200,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 12,
    backgroundColor: Colors.card,
    overflow: 'hidden',
  },
  bannerOverlay: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    lineHeight: 28,
  },
  bannerSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  searchContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.text,
  },
  servicesContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  servicesRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  serviceItem: {
    width: (width - 80) / 4,
    alignItems: 'center',
  },
  serviceIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    backgroundColor: Colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  highlightedService: {
    // No additional styling needed for highlighted services in this layout
  },
  highlightedIcon: {
    backgroundColor: Colors.primary,
  },
  serviceText: {
    fontSize: 11,
    color: Colors.text,
    textAlign: 'center',
    lineHeight: 14,
  },
  highlightedText: {
    color: Colors.text,
  },
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingBottom: 20,
    paddingTop: 10,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    color: Colors.text,
    marginTop: 4,
  },
}); 