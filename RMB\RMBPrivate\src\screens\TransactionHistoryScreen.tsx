import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';

interface TransactionHistoryScreenProps {
  navigation: any;
  route: any;
}

export const TransactionHistoryScreen: React.FC<TransactionHistoryScreenProps> = ({ 
  navigation, 
  route 
}) => {
  const [activeTab, setActiveTab] = useState('Transaction History');
  const { accountName } = route.params || { accountName: 'Account' };

  const tabs = ['Transaction History', 'Account Options'];

  const transactions: any[] = [];

  const formatCurrency = (amount: number) => {
    const sign = amount < 0 ? '-' : '';
    return `${sign}R${Math.abs(amount).toFixed(2)}`;
  };

  const renderTransaction = (transaction: any) => (
    <TouchableOpacity
      key={transaction.id}
      style={styles.transactionItem}
      activeOpacity={0.7}
    >
      <View style={styles.transactionLeft}>
        <Text style={styles.transactionType}>{transaction.type}</Text>
        <Text style={styles.transactionDate}>{transaction.date}</Text>
      </View>
      <View style={styles.transactionRight}>
        <Text style={[
          styles.transactionAmount,
          { color: transaction.amount < 0 ? '#ff4757' : '#2ed573' }
        ]}>
          {formatCurrency(transaction.amount)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.background} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Accounts</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerIcon}>
            <Ionicons name="search-outline" size={24} color={Colors.textMuted} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerIcon}>
            <Ionicons name="close" size={24} color={Colors.textMuted} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[
              styles.tab,
              activeTab === tab && styles.activeTab
            ]}
            onPress={() => setActiveTab(tab)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.tabText,
              activeTab === tab && styles.activeTabText
            ]}>
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {activeTab === 'Transaction History' ? (
          <View style={styles.transactionHistory}>
            {/* Filter */}
            <View style={styles.filterContainer}>
              <TouchableOpacity style={styles.filterButton} activeOpacity={0.7}>
                <Text style={styles.filterText}>Older</Text>
                <Ionicons name="chevron-down" size={16} color={Colors.textMuted} />
              </TouchableOpacity>
            </View>

            {/* Transactions */}
            <View style={styles.transactionsList}>
              {transactions.map(renderTransaction)}
            </View>

            {/* Empty State */}
            {transactions.length === 0 && (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>No transactions to show</Text>
              </View>
            )}
          </View>
        ) : (
          <View style={styles.accountOptions}>
            {/* Account Card */}
            <View style={styles.accountCard}>
              <View style={styles.cardContainer}>
                <Image 
                  source={require('../../images/RMB_card.png')} 
                  style={styles.rmbCardImage}
                  resizeMode="contain"
                />
              </View>
              
              <View style={styles.accountInfo}>
                <Text style={styles.accountTitle}>{accountName}</Text>
                <View style={styles.accountDetailsRow}>
                  <Text style={styles.accountLabel}>Account Number</Text>
                  <Text style={styles.accountValue}>Pending Update</Text>
                </View>
                <View style={styles.accountDetailsRow}>
                  <Text style={styles.accountLabel}>Balance</Text>
                  <Text style={styles.accountValue}>R0</Text>
                </View>
                <View style={styles.accountDetailsRow}>
                  <Text style={styles.accountLabel}>Available</Text>
                  <Text style={styles.accountValue}>R0</Text>
                </View>
              </View>
            </View>

            {/* Options Grid */}
            <View style={styles.optionsGrid}>
              {/* Row 1 */}
              <View style={styles.optionsRow}>
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="add-circle-outline" size={24} color="#ff4757" />
                  </View>
                  <Text style={styles.optionText}>Add Business{'\n'}Call Account</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="card-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>My Cards</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="basket-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Buy</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="hand-left-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Pay</Text>
                </TouchableOpacity>
              </View>

              {/* Row 2 */}
              <View style={styles.optionsRow}>
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="swap-horizontal-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Transfer</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="receipt-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Detailed{'\n'}Balance</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="card" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>My Debit{'\n'}Orders</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="stats-chart-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Debit order{'\n'}mandates</Text>
                </TouchableOpacity>
              </View>

              {/* Row 3 */}
              <View style={styles.optionsRow}>
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="stop-circle-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Stop Payment{'\n'}History</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="information-circle-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Account{'\n'}Information</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="settings-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Account{'\n'}Settings</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="document-text-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Statement{'\n'}History</Text>
                </TouchableOpacity>
              </View>

              {/* Row 4 */}
              <View style={styles.optionsRow}>
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="download-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Download{'\n'}Statement</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="print-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Print{'\n'}Statement</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="mail-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Email{'\n'}Statement</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.optionItem} activeOpacity={0.7}>
                  <View style={styles.optionIcon}>
                    <Ionicons name="bar-chart-outline" size={24} color="#4db6ac" />
                  </View>
                  <Text style={styles.optionText}>Income and{'\n'}Expenditure</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Bottom Navigation */}
      <View style={styles.bottomNav}>
        <TouchableOpacity 
          style={styles.navItem}
          onPress={() => navigation.navigate('FNBHome')}
        >
          <Ionicons name="home-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="business-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Bank</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="chatbubble-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Message</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="person-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>My profile</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Ionicons name="menu-outline" size={24} color={Colors.textMuted} />
          <Text style={styles.navText}>Menu</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: Colors.background,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    textAlign: 'center',
    marginLeft: -32, // Offset for back button
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerIcon: {
    marginLeft: 16,
    padding: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    marginHorizontal: 20,
    borderRadius: 8,
    padding: 4,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: Colors.textMuted,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.background,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  transactionHistory: {
    flex: 1,
  },
  filterContainer: {
    marginBottom: 20,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  filterText: {
    fontSize: 14,
    color: Colors.textMuted,
    marginRight: 8,
    fontWeight: '500',
  },
  transactionsList: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    overflow: 'hidden',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  transactionLeft: {
    flex: 1,
  },
  transactionType: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: Colors.textMuted,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.textMuted,
  },
  accountOptions: {
    flex: 1,
  },
  accountCard: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  cardContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  rmbCardImage: {
    width: 230,
    height: 144,
  },

  accountInfo: {
    marginTop: 10,
  },
  accountTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  accountDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  accountLabel: {
    fontSize: 14,
    color: Colors.textMuted,
  },
  accountValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  optionsGrid: {
    flex: 1,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  optionItem: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 8,
    marginHorizontal: 4,
  },
  optionIcon: {
    marginBottom: 8,
  },
  optionText: {
    fontSize: 12,
    color: Colors.text,
    textAlign: 'center',
    lineHeight: 16,
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingBottom: 20,
    paddingTop: 10,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navText: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
}); 