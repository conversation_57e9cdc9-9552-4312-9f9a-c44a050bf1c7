import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../utils/colors';
import { HARDCODED_ACCOUNTS } from '../utils/data';

interface AccountsScreenProps {
  navigation: any;
}

export const AccountsScreen: React.FC<AccountsScreenProps> = ({ navigation }) => {
  const formatCurrency = (amount: number) => {
    return `R ${Math.abs(amount).toLocaleString('en-ZA', { minimumFractionDigits: 2 })}`;
  };

  const formatAccountNumber = (accountNumber: string) => {
    return accountNumber.replace(/(.{4})/g, '$1 ').trim();
  };

  const renderAccount = (account: any) => (
    <TouchableOpacity
      key={account.id}
      style={styles.accountCard}
      onPress={() => navigation.navigate('Account', { accountId: account.id })}
      activeOpacity={0.8}
    >
      <LinearGradient
        colors={[Colors.card, Colors.surface]}
        style={styles.accountCardGradient}
      >
        <View style={styles.accountHeader}>
          <View style={styles.accountIcon}>
            <Ionicons 
              name={account.type === 'credit' ? 'card' : 'wallet'} 
              size={24} 
              color={Colors.primary} 
            />
          </View>
          <View style={styles.accountInfo}>
            <Text style={styles.accountName}>{account.name}</Text>
            <Text style={styles.accountNumber}>{formatAccountNumber(account.accountNumber)}</Text>
          </View>
          <View style={styles.accountBalance}>
            <Text style={[
              styles.balanceAmount,
              { color: account.balance >= 0 ? Colors.success : Colors.error }
            ]}>
              {account.balance >= 0 ? formatCurrency(account.balance) : `-${formatCurrency(account.balance)}`}
            </Text>
            <Text style={styles.balanceLabel}>Available Balance</Text>
          </View>
        </View>
        
        <View style={styles.accountActions}>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="paper-plane-outline" size={16} color={Colors.primary} />
            <Text style={styles.actionText}>Transfer</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="list-outline" size={16} color={Colors.primary} />
            <Text style={styles.actionText}>History</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="card-outline" size={16} color={Colors.primary} />
            <Text style={styles.actionText}>Details</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Accounts</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add-circle-outline" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.accountsList}>
          {HARDCODED_ACCOUNTS.map(renderAccount)}
        </View>
        
        <View style={styles.summary}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Total Balance</Text>
            <Text style={styles.summaryAmount}>
              {formatCurrency(HARDCODED_ACCOUNTS.reduce((sum, acc) => sum + Math.max(0, acc.balance), 0))}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.text,
  },
  addButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  accountsList: {
    padding: 20,
  },
  accountCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  accountCardGradient: {
    padding: 20,
  },
  accountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  accountIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  accountNumber: {
    fontSize: 14,
    color: Colors.textMuted,
    marginTop: 4,
  },
  accountBalance: {
    alignItems: 'flex-end',
  },
  balanceAmount: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  balanceLabel: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 2,
  },
  accountActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: Colors.divider,
    paddingTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  actionText: {
    fontSize: 12,
    color: Colors.primary,
    marginLeft: 6,
    fontWeight: '500',
  },
  summary: {
    padding: 20,
  },
  summaryCard: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  summaryAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.success,
  },
}); 