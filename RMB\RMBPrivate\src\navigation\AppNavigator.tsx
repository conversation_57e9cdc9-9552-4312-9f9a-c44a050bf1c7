import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { Colors } from '../utils/colors';
import { LoginScreen } from '../screens/LoginScreen';
import { DashboardScreen } from '../screens/DashboardScreen';
import { BankingHomeScreen } from '../screens/BankingHomeScreen';
import { FNBHomeScreen } from '../screens/FNBHomeScreen';
import { TransactionHistoryScreen } from '../screens/TransactionHistoryScreen';
import { AccountsScreen } from '../screens/AccountsScreen';
import { TransferScreen } from '../screens/TransferScreen';
import { TransactionsScreen } from '../screens/TransactionsScreen';
import { MoreScreen } from '../screens/MoreScreen';

export type RootStackParamList = {
  Dashboard: undefined;
  Login: undefined;
  BankingHome: undefined;
  FNBHome: undefined;
  TransactionHistory: { accountName: string };
  Accounts: undefined;
  Transfer: undefined;
  Transactions: undefined;
  More: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export const AppNavigator = () => {
  return (
    <NavigationContainer
      theme={{
        dark: true,
        colors: {
          primary: Colors.primary,
          background: Colors.background,
          card: Colors.surface,
          text: Colors.text,
          border: Colors.border,
          notification: Colors.primary,
        },
        fonts: {
          regular: {
            fontFamily: 'System',
            fontWeight: '400',
          },
          medium: {
            fontFamily: 'System',
            fontWeight: '500',
          },
          bold: {
            fontFamily: 'System',
            fontWeight: '700',
          },
          heavy: {
            fontFamily: 'System',
            fontWeight: '900',
          },
        },
      }}
    >
      <StatusBar style="light" backgroundColor={Colors.background} />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          cardStyle: { backgroundColor: Colors.background },
        }}
        initialRouteName="Dashboard"
      >
        <Stack.Screen name="Dashboard" component={DashboardScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="BankingHome" component={BankingHomeScreen} />
        <Stack.Screen name="FNBHome" component={FNBHomeScreen} />
        <Stack.Screen name="TransactionHistory" component={TransactionHistoryScreen} />
        <Stack.Screen name="Accounts" component={AccountsScreen} />
        <Stack.Screen name="Transfer" component={TransferScreen} />
        <Stack.Screen name="Transactions" component={TransactionsScreen} />
        <Stack.Screen name="More" component={MoreScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}; 