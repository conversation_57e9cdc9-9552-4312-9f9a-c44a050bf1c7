export const Colors = {
  // Primary black theme
  background: '#000000',
  surface: '#1a1a1a',
  card: '#2a2a2a',
  
  // RMB Brand colors adapted for dark theme
  primary: '#ff7b00', // RMB Orange
  primaryLight: '#ff9500',
  primaryDark: '#e66900',
  
  // Secondary colors
  secondary: '#333333',
  accent: '#4a4a4a',
  
  // Text colors
  text: '#ffffff',
  textSecondary: '#cccccc',
  textMuted: '#999999',
  
  // Status colors
  success: '#00c851',
  warning: '#ffbb33',
  error: '#ff4444',
  info: '#33b5e5',
  
  // Border and divider
  border: '#404040',
  divider: '#2a2a2a',
  
  // Input fields
  inputBackground: '#1a1a1a',
  inputBorder: '#404040',
  inputText: '#ffffff',
  placeholder: '#666666',
  
  // Navigation
  tabBarActive: '#ff6b35',
  tabBarInactive: '#666666',
  
  // Shadows and overlays
  overlay: 'rgba(0, 0, 0, 0.8)',
  shadow: 'rgba(255, 255, 255, 0.1)',
}; 