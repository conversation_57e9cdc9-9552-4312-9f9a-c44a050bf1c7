# RMB Private Banking App

A modern South African banking application clone inspired by FNB, built with React Native and Expo. Features a sleek black theme optimized for premium banking experience.

## Features

### 🔐 Authentication
- Secure PIN-based login
- 5-digit PIN authentication
- Hardcoded demo PIN: **07045**

### 🏦 Banking Core Features
- **Dashboard**: Overview of accounts, recent transactions, and quick actions
- **Account Management**: View multiple accounts (Current, Savings, Credit Card)
- **Money Transfer**: Transfer funds between accounts and to beneficiaries
- **Transaction History**: Detailed transaction history with filtering
- **More Services**: Additional banking services and profile management

### 🎨 UI/UX Features
- Premium black theme design
- RMB Orange accent colors (#ff6b35)
- Smooth animations and transitions
- Touch-friendly interface
- South African Rand (ZAR) currency formatting

## Demo Credentials

- **PIN**: `07045`
- **User**: <PERSON>
- **Account Number**: ****9012

## Tech Stack

- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and build tools
- **TypeScript**: Type-safe JavaScript
- **React Navigation**: Navigation framework
- **Expo Linear Gradient**: Beautiful gradient effects
- **Expo Vector Icons**: Icon library

## Project Structure

```
src/
├── components/          # Reusable UI components
├── screens/            # App screens
│   ├── LoginScreen.tsx
│   ├── DashboardScreen.tsx
│   ├── AccountsScreen.tsx
│   ├── TransferScreen.tsx
│   ├── TransactionsScreen.tsx
│   └── MoreScreen.tsx
├── navigation/         # Navigation configuration
│   ├── AppNavigator.tsx
│   └── TabNavigator.tsx
├── utils/             # Utilities and constants
│   ├── colors.ts      # Theme colors
│   └── data.ts        # Hardcoded demo data
└── types/             # TypeScript type definitions
    └── index.ts
```

## Installation & Setup

1. **Clone and navigate to the project**
   ```bash
   cd RMBPrivate
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   - **iOS**: `npm run ios`
   - **Android**: `npm run android`
   - **Web**: `npm run web`

## Building for Production

### Android APK using Expo Go

1. **Install EAS CLI**
   ```bash
   npm install -g @expo/eas-cli
   ```

2. **Login to Expo**
   ```bash
   eas login
   ```

3. **Configure build**
   ```bash
   eas build:configure
   ```

4. **Build for Android**
   ```bash
   eas build --platform android --profile preview
   ```

### Using Expo Build Service

1. **Build APK**
   ```bash
   expo build:android
   ```

2. **Build iOS**
   ```bash
   expo build:ios
   ```

## App Screens

### 1. Login Screen
- PIN-based authentication
- Custom numeric keypad
- RMB branding with black theme
- PIN hint displayed for demo purposes

### 2. Dashboard
- Total balance overview
- Quick actions (Transfer, Pay, Scan QR, Buy Airtime)
- Account cards carousel
- Recent transactions list

### 3. Accounts
- All user accounts with balances
- Account type indicators
- Quick action buttons per account

### 4. Transfer
- Select from/to accounts
- Amount input with quick amount buttons
- Beneficiary selection
- Transfer confirmation

### 5. Transactions
- Complete transaction history
- Filter by type (All, Credits, Debits)
- Transaction summaries

### 6. More
- User profile information
- Banking services menu
- Investment options
- Support and settings
- Logout functionality

## Color Scheme

The app uses a premium black theme with RMB-inspired colors:

- **Background**: #000000 (Pure Black)
- **Surface**: #1a1a1a (Dark Gray)
- **Primary**: #ff6b35 (RMB Orange)
- **Text**: #ffffff (White)
- **Success**: #00c851 (Green)
- **Error**: #ff4444 (Red)

## Demo Data

The app includes realistic demo data:
- 3 Different account types (Current, Savings, Credit Card)
- 6 Sample transactions
- 3 Beneficiaries for transfers
- User profile with South African banking details

## Development Notes

- All data is hardcoded for demo purposes
- PIN authentication is simulated
- Transfer functionality shows confirmation dialogs
- App is optimized for mobile devices
- Dark theme is enforced throughout

## Future Enhancements

- Real backend integration
- Biometric authentication
- Push notifications
- QR code scanning
- Bill payments
- Investment tracking
- Multi-language support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is a demo application created for educational purposes. All branding and logos are for demonstration only.

---

**Disclaimer**: This is a clone/demo application and is not affiliated with RMB Bank or any financial institution. Do not use for actual banking purposes. 